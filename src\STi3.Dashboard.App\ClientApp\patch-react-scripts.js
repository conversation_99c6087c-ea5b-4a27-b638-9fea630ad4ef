const fs = require('fs');
const path = require('path');

// Caminho para o arquivo problemático
const filePath = path.join(__dirname, 'node_modules', 'react-scripts', 'scripts', 'utils', 'verifyTypeScriptSetup.js');

if (fs.existsSync(filePath)) {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Substitui a linha problemática
  const problematicLine = 'appTsConfig.compilerOptions[option] = value;';
  const fixedLine = `
    try {
      appTsConfig.compilerOptions[option] = value;
    } catch (e) {
      // Ignora erro de propriedade somente leitura
      console.warn('Warning: Could not set TypeScript option', option, 'to', value);
    }
  `;
  
  if (content.includes(problematicLine)) {
    content = content.replace(problematicLine, fixedLine);
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('React Scripts patch aplicado com sucesso!');
  } else {
    console.log('Linha problemática não encontrada. O arquivo pode já estar corrigido.');
  }
} else {
  console.log('Arquivo verifyTypeScriptSetup.js não encontrado.');
}
